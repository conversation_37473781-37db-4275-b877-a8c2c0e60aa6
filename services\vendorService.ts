import { VendorApiResponse } from "@/lib/types";

const API_BASE_URL = "http://localhost:3000/api";

export const fetchVendors = async (): Promise<VendorApiResponse> => {
  const response = await fetch(`${API_BASE_URL}/vendors`);

  if (!response.ok) {
    throw new Error(`Failed to fetch vendors: ${response.statusText}`);
  }

  const data: VendorApiResponse = await response.json();
  return data;
};
