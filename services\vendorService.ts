import { VendorApiResponse } from "@/lib/types";

const API_BASE_URL = "http://localhost:3000/api"; // As per user instruction

interface FetchVendorsParams {
  page?: number;
  limit?: number;
  search?: string;
  verificationStatus?: string;
  kycStatus?: string;
  isActive?: boolean;
}

export const fetchVendors = async (params: FetchVendorsParams = {}): Promise<VendorApiResponse> => {
  const queryParams = new URLSearchParams();

  if (params.page) queryParams.append("page", params.page.toString());
  if (params.limit) queryParams.append("limit", params.limit.toString());
  if (params.search) queryParams.append("search", params.search);
  if (params.verificationStatus) queryParams.append("verificationStatus", params.verificationStatus);
  if (params.kycStatus) queryParams.append("kycStatus", params.kycStatus);
  if (params.isActive !== undefined) queryParams.append("isActive", params.isActive.toString());

  const response = await fetch(`${API_BASE_URL}/vendors?${queryParams.toString()}`);

  if (!response.ok) {
    // In a real app, you might want to throw a more specific error or handle it differently
    throw new Error(`Failed to fetch vendors: ${response.statusText}`);
  }

  const data: VendorApiResponse = await response.json();
  return data;
}; 