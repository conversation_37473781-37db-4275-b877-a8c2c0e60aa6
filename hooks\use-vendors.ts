import { useState, useEffect } from "react";
import { VendorApiResponse, Vendor } from "@/lib/types";
import { fetchVendors } from "@/services/vendorService";

interface UseVendorsReturn {
  vendors: Vendor[];
  isLoading: boolean;
  error: Error | null;
}

export function useVendors(): UseVendorsReturn {
  const [data, setData] = useState<VendorApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const result = await fetchVendors();
        setData(result);
      } catch (e) {
        setError(e as Error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return {
    vendors: data?.items || [],
    isLoading,
    error,
  };
}
