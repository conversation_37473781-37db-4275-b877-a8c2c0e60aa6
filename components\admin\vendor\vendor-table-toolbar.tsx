'use client'

import * as React from "react"
import { Table as TanstackTable } from "@tanstack/react-table"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { XIcon } from "lucide-react"

interface VendorTableToolbarProps<TData> {
  table: TanstackTable<TData>
  search: string;
  setSearch: (value: string) => void;
  verificationStatus: string;
  setVerificationStatus: (value: string) => void;
  kycStatus: string;
  setKycStatus: (value: string) => void;
  isActive: boolean | undefined;
  setIsActive: (value: boolean | undefined) => void;
  refetch: () => void;
}

export function VendorTableToolbar<TData>({
  table,
  search,
  setSearch,
  verificationStatus,
  setVerificationStatus,
  kycStatus,
  setKycStatus,
  isActive,
  setIsActive,
  refetch
}: VendorTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0 || search !== '' || verificationStatus !== '' || kycStatus !== '' || isActive !== undefined;

  const verificationStatuses = ["PENDING", "APPROVED", "REJECTED"];
  const kycStatuses = ["PENDING", "PARTIAL", "COMPLETE", "REJECTED"];

  const handleResetFilters = () => {
    setSearch('');
    setVerificationStatus('');
    setKycStatus('');
    setIsActive(undefined);
    table.resetColumnFilters(); // Resets table's internal filters if any are used
    // refetch(); // Optionally refetch if needed after reset
  }

  return (
    <div className="flex items-center justify-between gap-2 py-4">
      <div className="flex flex-1 items-center space-x-2 flex-wrap">
        <Input
          placeholder="Search vendors..."
          value={search}
          onChange={(event) => setSearch(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px] mb-2 sm:mb-0"
        />
        <Select
          value={verificationStatus}
          onValueChange={setVerificationStatus}
        >
          <SelectTrigger className="h-8 w-full sm:w-[180px] mb-2 sm:mb-0">
            <SelectValue placeholder="Verification Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Verification</SelectItem>
            {verificationStatuses.map(status => (
              <SelectItem key={status} value={status}>{status}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={kycStatus} onValueChange={setKycStatus}>
          <SelectTrigger className="h-8 w-full sm:w-[180px] mb-2 sm:mb-0">
            <SelectValue placeholder="KYC Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All KYC</SelectItem>
            {kycStatuses.map(status => (
              <SelectItem key={status} value={status}>{status}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select 
            value={isActive === undefined ? "all" : isActive ? "true" : "false"}
            onValueChange={(value) => {
                if (value === "all") setIsActive(undefined);
                else setIsActive(value === "true");
            }}
        >
          <SelectTrigger className="h-8 w-full sm:w-[120px] mb-2 sm:mb-0">
            <SelectValue placeholder="Active Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Active</SelectItem>
            <SelectItem value="true">Active</SelectItem>
            <SelectItem value="false">Inactive</SelectItem>
          </SelectContent>
        </Select>
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={handleResetFilters}
            className="h-8 px-2 lg:px-3 mb-2 sm:mb-0"
          >
            Reset
            <XIcon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      {/* Optional: Button to manually refresh data */}
      {/* <Button onClick={refetch} variant="outline" className="h-8">
        <RefreshCwIcon className="mr-2 h-4 w-4" /> Refresh
      </Button> */}
    </div>
  )
}
