'use client'

import * as React from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { Vendor } from "@/lib/types"

interface VendorTableProps {
  columns: ColumnDef<Vendor>[]
  data: Vendor[]
  isLoading: boolean
  error: Error | null
}

export function VendorTable({
  columns,
  data,
  isLoading,
  error,
  totalRecords,
  currentPage,
  limit,
  setPage,
  setLimit,
  search,
  setSearch,
  verificationStatus,
  setVerificationStatus,
  kycStatus,
  setKycStatus,
  isActive,
  setIsActive,
  refetch,
}: VendorTableProps) {
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      // pagination is handled server-side, so we don't set it in table state directly
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters, // We might not use client-side filtering if all is server-side
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(), // For client-side filtering if needed, but primary is server-side
    getPaginationRowModel: getPaginationRowModel(), // Still useful for UI elements even with server-side data
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: true, // IMPORTANT: Let server handle pagination
    manualFiltering: true, // IMPORTANT: Let server handle filtering
    manualSorting: false, // Allow client-side sorting for now, can be changed to true if server supports it
    pageCount: Math.ceil(totalRecords / limit), // For Tanstack Table to know total pages
  })

  return (
    <div className="space-y-4">
      <VendorTableToolbar
        table={table}
        search={search}
        setSearch={setSearch}
        verificationStatus={verificationStatus}
        setVerificationStatus={setVerificationStatus}
        kycStatus={kycStatus}
        setKycStatus={setKycStatus}
        isActive={isActive}
        setIsActive={setIsActive}
        refetch={refetch}
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: limit }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  {columns.map((column, colIndex) => (
                    <TableCell key={`skeleton-cell-${index}-${colIndex}`}>
                      <Skeleton className="h-6 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : error ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-red-500">
                  Error fetching vendors: {error.message}
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <VendorTablePagination
        table={table}
        totalRecords={totalRecords}
        currentPage={currentPage}
        limit={limit}
        setPage={setPage}
        setLimit={setLimit}
      />
    </div>
  )
}
