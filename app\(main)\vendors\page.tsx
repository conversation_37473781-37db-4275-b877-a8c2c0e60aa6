'use client'

import React from 'react';
import { useVendors } from '@/hooks/use-vendors';
import { columns } from '@/components/admin/vendor/vendor-columns';
import { VendorTable } from '@/components/admin/vendor/vendor-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// export const metadata = {
//   title: "Vendors",
//   description: "Manage and view all registered vendors.",
// };

export default function VendorsPage() {
  const {
    vendors,
    total,
    currentPage,
    limit,
    isLoading,
    error,
    setPage,
    setLimit,
    search,
    setSearch,
    verificationStatus,
    setVerificationStatus,
    kycStatus,
    setKycStatus,
    isActive,
    setIsActive,
    refetch,
  } = useVendors(); // Use default initial params from the hook

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>Vendors</CardTitle>
          <CardDescription>
            Manage and view all registered vendors.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VendorTable
            columns={columns}
            data={vendors}
            isLoading={isLoading}
            error={error}
            totalRecords={total}
            currentPage={currentPage}
            limit={limit}
            setPage={setPage}
            setLimit={setLimit}
            search={search}
            setSearch={setSearch}
            verificationStatus={verificationStatus}
            setVerificationStatus={setVerificationStatus}
            kycStatus={kycStatus}
            setKycStatus={setKycStatus}
            isActive={isActive}
            setIsActive={setIsActive}
            refetch={refetch}
          />
        </CardContent>
      </Card>
    </div>
  );
}
