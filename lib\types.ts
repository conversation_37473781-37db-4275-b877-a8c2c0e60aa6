// Simplified vendor types - only essential fields
export interface Vendor {
  id: string;
  businessName: string;
  contactPersonName: string;
  email: string;
  mobile: string;
  verificationStatus: "PENDING" | "APPROVED" | "REJECTED";
  kycStatus: "PENDING" | "PARTIAL" | "COMPLETE" | "REJECTED";
  isActive: boolean;
}

export interface VendorApiResponse {
  items: Vendor[];
  total: number;
  page: number;
  limit: number;
}
